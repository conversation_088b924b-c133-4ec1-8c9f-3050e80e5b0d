{"name": "tanstack-start-example-basic-react-query", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev:staging": "vinxi dev --mode staging", "build": "vinxi build", "start": "vinxi start"}, "dependencies": {"@shopify/app-bridge-react": "^4.1.10", "@shopify/app-bridge-utils": "^3.5.1", "@shopify/polaris": "^13.9.5", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.66.0", "@tanstack/react-router": "^1.119.0", "@tanstack/react-router-devtools": "^1.119.1", "@tanstack/react-router-with-query": "^1.119.0", "@tanstack/react-start": "^1.119.0", "@tanstack/zod-adapter": "^1.119.0", "axios": "^1.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.2.0", "vinxi": "^0.5.6", "zod": "^3.24.3", "zustand": "^5.0.4"}, "devDependencies": {"@shopify/app-bridge-types": "^0.0.18", "@shopify/app-bridge-ui-types": "^0.0.1", "@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.2", "vite-tsconfig-paths": "^5.1.4"}}