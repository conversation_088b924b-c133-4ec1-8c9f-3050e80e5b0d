import { useQueryClient } from "@tanstack/react-query";
import {
	NavigateOptions,
	useNavigate as useNavigateTanstack,
} from "@tanstack/react-router";

type CustomNavigateOptions = Omit<NavigateOptions, "search"> & {
	search?: Record<string, string>;
};

export const useNavigate = () => {
	const navigateTanstack = useNavigateTanstack();
	const queryClient = useQueryClient();
	const dataContext = queryClient.getQueryData(["shopify-api-key"]) as {
		shop: string;
		admin: string;
		host: string;
	};

	const commonSearch = {
		shop: dataContext?.shop,
		admin: dataContext?.admin,
		host: dataContext?.host,
	};

	const navigate = (data: CustomNavigateOptions) => {
		navigateTanstack({
			...data,
			to: data.to,
			search: {
				...commonSearch,
				...(data.search || {}),
			},
		});
	};

	const navigateAndRemoveSearch = (
		data: CustomNavigateOptions,
		removeSearch: string[]
	) => {
		navigateTanstack({
			...data,
			to: data.to,
			search: {
				...commonSearch,
				...Object.fromEntries(
					Object.entries(data.search || {}).filter(
						([key]) => !removeSearch.includes(key)
					)
				),
			},
		});
	};

	return { navigate, navigateAndRemoveSearch };
};
