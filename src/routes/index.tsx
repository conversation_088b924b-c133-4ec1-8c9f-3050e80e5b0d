import { Page } from "@shopify/polaris";
import { useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";

import { useNavigate } from "~/hooks/use-navigate";

export const postsQueryOptions = {
  queryKey: ["posts"],
  queryFn: async () => {
    const response = await fetch("https://jsonplaceholder.typicode.com/posts");
    if (!response.ok) {
      throw new Error("Failed to fetch posts");
    }
    return response.json();
  }
};

export const Route = createFileRoute("/")({
  component: Home,

  loader: async ({ context }) => {
    await context.queryClient.prefetchQuery(postsQueryOptions);
  },

  pendingComponent: () => {
    return <div>Loading...</div>;
  }
});

function Home() {
  const { data } = useSuspenseQuery<{ title: string; id: number }[]>(postsQueryOptions);

  const { navigate, navigateAndRemoveSearch } = useNavigate();

  return (
    <Page>
      {data.map((post) => (
        <div key={post.id}>{post.title}</div>
      ))}
    </Page>
  );
}
