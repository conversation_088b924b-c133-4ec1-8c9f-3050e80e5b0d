import { createFileRoute } from "@tanstack/react-router";

export const Route = createFileRoute("/error")({
	component: RouteComponent,
});

function RouteComponent() {
	const { message } = Route.useSearch() as { message?: string };

	return (
		<div className="p-8 max-w-md mx-auto mt-10 bg-red-50 text-red-800 rounded-lg shadow-md">
			<h2 className="text-2xl font-bold mb-4">Error</h2>
			<p className="mb-4">{message || "An error occurred"}</p>
			<div className="mt-6">
				<a
					href="/"
					className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
					Go to Home Page
				</a>
			</div>
			<div className="mt-4 text-sm text-gray-600">
				<p>
					Try adding{" "}
					<code className="bg-gray-200 px-1 rounded">?shop=yourshopname</code>{" "}
					to the URL
				</p>
			</div>
		</div>
	);
}
